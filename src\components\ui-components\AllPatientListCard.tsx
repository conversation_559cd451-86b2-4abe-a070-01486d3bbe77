import * as React from "react";
import { cn } from "@/lib/utils";
import { MoreHorizontal, Edit, Trash2, Phone, Mail } from "lucide-react";

export interface Patient {
	id: string;
	name: string;
	email: string;
	phone: string;
	status: "Active" | "Inactive";
	lastVisit: string;
	syncStatus: "synced" | "failed" | "pending";
}

export interface PatientAction {
	type: "edit" | "delete" | "call" | "email";
	onClick: (patient: Patient) => void;
	disabled?: boolean;
}

export interface AllPatientListCardProps
	extends React.HTMLAttributes<HTMLDivElement> {
	patient: Patient;
	actions?: PatientAction[];
	showCheckbox?: boolean;
	onCheckboxChange?: (patientId: string, checked: boolean) => void;
	checked?: boolean;
	variant?: "default" | "compact";
}

const AllPatientListCard = React.forwardRef<
	HTMLDivElement,
	AllPatientListCardProps
>(
	(
		{
			className,
			patient,
			actions = [],
			showCheckbox = true,
			onCheckboxChange,
			checked = false,
			variant = "default",
			...props
		},
		ref
	) => {
		const defaultActions: PatientAction[] = [
			{
				type: "edit",
				onClick: (patient) => console.log("Edit", patient.id),
			},
			{
				type: "call",
				onClick: (patient) => console.log("Call", patient.phone),
			},
			{
				type: "email",
				onClick: (patient) => console.log("Email", patient.email),
			},
			{
				type: "delete",
				onClick: (patient) => console.log("Delete", patient.id),
			},
		];

		const finalActions = actions.length > 0 ? actions : defaultActions;

		const getActionIcon = (type: PatientAction["type"]) => {
			switch (type) {
				case "edit":
					return <Edit className="h-3 w-3 text-gray-500" />;
				case "call":
					return <Phone className="h-3 w-3 text-gray-500" />;
				case "email":
					return <Mail className="h-3 w-3 text-gray-500" />;
				case "delete":
					return <Trash2 className="h-3 w-3 text-gray-500" />;
				default:
					return <MoreHorizontal className="h-3 w-3 text-gray-500" />;
			}
		};

		const getInitials = (name: string) => {
			return name
				.split(" ")
				.map((n) => n[0])
				.join("")
				.toUpperCase();
		};

		return (
			<div
				ref={ref}
				className={cn(
					"flex items-center border-t border-gray-200 bg-white transition-colors hover:bg-gray-50",
					variant === "default" ? "h-16" : "h-12",
					className
				)}
				{...props}
			>
				{/* Checkbox */}
				{showCheckbox && (
					<div className="flex items-center px-4">
						<input
							type="checkbox"
							checked={checked}
							onChange={(e) =>
								onCheckboxChange?.(patient.id, e.target.checked)
							}
							className="h-2.5 w-2.5 cursor-pointer rounded-sm border border-gray-300 bg-white hover:border-gray-400"
						/>
					</div>
				)}

				{/* Name Column */}
				<div className="flex w-72 min-w-20 items-center gap-3 px-3">
					<div className="flex h-9 w-9 items-center justify-center overflow-hidden rounded-full bg-gray-100">
						<div className="text-xs font-medium text-gray-600">
							{getInitials(patient.name)}
						</div>
					</div>
					<div className="flex flex-1 items-center gap-1">
						<div className="text-sm font-medium text-gray-900">
							{patient.name}
						</div>
						<div
							className={cn(
								"h-1.5 w-1.5 rounded-full",
								patient.syncStatus === "synced" &&
									"bg-green-500",
								patient.syncStatus === "failed" && "bg-red-500",
								patient.syncStatus === "pending" &&
									"bg-yellow-500"
							)}
						/>
					</div>
				</div>

				{/* Status Column */}
				<div className="flex w-32 min-w-20 items-center px-3">
					<div
						className={cn(
							"flex items-center rounded-md px-2 py-1",
							patient.status === "Active"
								? "bg-green-50 text-green-700"
								: "bg-gray-100 text-gray-600"
						)}
					>
						<div className="text-[10px] font-medium">
							{patient.status}
						</div>
					</div>
				</div>

				{/* Email Column */}
				<div className="flex w-60 min-w-20 items-center px-3">
					<div className="flex-1">
						<div className="text-xs text-gray-500">
							{patient.email}
						</div>
					</div>
				</div>

				{/* Phone Column */}
				<div className="flex w-40 min-w-20 items-center px-3">
					<div className="flex-1">
						<div className="text-xs text-gray-500">
							{patient.phone}
						</div>
					</div>
				</div>

				{/* Last Visit Column */}
				<div className="flex w-36 min-w-20 items-center px-3">
					<div className="flex-1">
						<div className="text-xs text-gray-500">
							{patient.lastVisit}
						</div>
					</div>
				</div>

				{/* Actions Column */}
				{finalActions.length > 0 && (
					<div className="flex min-w-16 items-center justify-end gap-1.5 px-3">
						{finalActions.map((action, index) => (
							<button
								key={index}
								onClick={() => action.onClick(patient)}
								disabled={action.disabled}
								className={cn(
									"flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-gray-50 p-1 transition-colors hover:bg-gray-100",
									action.disabled &&
										"cursor-not-allowed opacity-50"
								)}
							>
								{getActionIcon(action.type)}
							</button>
						))}
					</div>
				)}
			</div>
		);
	}
);

AllPatientListCard.displayName = "AllPatientListCard";

export { AllPatientListCard };
