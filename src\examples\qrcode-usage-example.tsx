import React from "react";
import { QRCode } from "@/components/common/QRCode";

export function QRCodeUsageExample() {
	return (
		<div className="space-y-8 p-8">
			<h1 className="text-2xl font-bold">QR Code Examples</h1>

			<div className="space-y-4">
				<h2 className="text-lg font-semibold">Your Original Example</h2>
				<QRCode
					value="https://app.migranium.com/schedule/university-of-waterloo-17-hellothiswaterloo"
					title="Title for my QR Code"
					size={128}
					bgColor="#ffffff"
					fgColor="#000000"
					level="L"
				/>
			</div>

			<div className="space-y-4">
				<h2 className="text-lg font-semibold">
					Using Predefined Sizes
				</h2>
				<div className="flex items-center gap-4">
					<div className="text-center">
						<QRCode
							value="https://example.com"
							size="sm"
							title="Small QR Code"
						/>
						<p className="mt-2 text-sm">Small (96px)</p>
					</div>
					<div className="text-center">
						<QRCode
							value="https://example.com"
							size="md"
							title="Medium QR Code"
						/>
						<p className="mt-2 text-sm">Medium (128px)</p>
					</div>
					<div className="text-center">
						<QRCode
							value="https://example.com"
							size="lg"
							title="Large QR Code"
						/>
						<p className="mt-2 text-sm">Large (192px)</p>
					</div>
				</div>
			</div>

			<div className="space-y-4">
				<h2 className="text-lg font-semibold">Custom Colors</h2>
				<div className="flex items-center gap-4">
					<QRCode
						value="https://example.com"
						bgColor="#1a1a1a"
						fgColor="#ffffff"
						title="Dark Theme QR Code"
					/>
					<QRCode
						value="https://example.com"
						bgColor="#e0f2fe"
						fgColor="#0369a1"
						title="Blue Theme QR Code"
					/>
				</div>
			</div>
		</div>
	);
}
