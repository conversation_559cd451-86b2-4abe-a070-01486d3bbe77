import { useState } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Uploader } from "@/components/common/Uploader";
import { getAllCountries } from "@/components/common/InputPhone/utils";
import type { CreateLocationRequest } from "../../types";
import type { UploadedFile } from "@/components/common/Uploader/types";

interface AddLocationSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CreateLocationRequest & { images?: File[] }) => void;
}

export function AddLocationSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddLocationSheetProps) {
	const [formData, setFormData] = useState<Partial<CreateLocationRequest>>({
		name: "",
		email: "",
		phone: "",
		address: {
			street: "",
			city: "",
			state: "",
			zipCode: "",
			country: "",
		},
		description: "",
		timezone: "America/New_York", // default timezone
	});
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [actualFiles, setActualFiles] = useState<File[]>([]);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const countries = getAllCountries();

	const handleInputChange = (field: string, value: string) => {
		if (field.startsWith("address.")) {
			const addressField = field.split(".")[1];
			setFormData((prev) => ({
				...prev,
				address: {
					...prev.address!,
					[addressField]: value,
				},
			}));
		} else {
			setFormData((prev) => ({
				...prev,
				[field]: value,
			}));
		}
	};

	const handleFilesChange = (files: File[]) => {
		const newFiles: UploadedFile[] = files.map((file) => ({
			id: Math.random().toString(36).substr(2, 9),
			name: file.name,
			size: file.size,
			type: file.type,
		}));
		setUploadedFiles((prev) => [...prev, ...newFiles]);
		setActualFiles((prev) => [...prev, ...files]);
	};

	const handleFileRemove = (fileId: string) => {
		const fileIndex = uploadedFiles.findIndex((file) => file.id === fileId);
		setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
		setActualFiles((prev) =>
			prev.filter((_, index) => index !== fileIndex)
		);
	};

	const handleFileEdit = (fileId: string, newName: string) => {
		setUploadedFiles((prev) =>
			prev.map((file) =>
				file.id === fileId ? { ...file, name: newName } : file
			)
		);
	};

	const handleSubmit = async () => {
		if (!formData.name || !formData.address?.street) {
			// Basic validation - in a real app, you'd want more comprehensive validation
			return;
		}

		setIsSubmitting(true);
		try {
			await onSubmit?.({
				...(formData as CreateLocationRequest),
				images: actualFiles,
			});

			// Reset form
			setFormData({
				name: "",
				email: "",
				phone: "",
				address: {
					street: "",
					city: "",
					state: "",
					zipCode: "",
					country: "",
				},
				description: "",
				timezone: "America/New_York",
			});
			setUploadedFiles([]);
			setActualFiles([]);
			onOpenChange(false);
		} catch (error) {
			console.error("Error submitting location:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		// Reset form
		setFormData({
			name: "",
			email: "",
			phone: "",
			address: {
				street: "",
				city: "",
				state: "",
				zipCode: "",
				country: "",
			},
			description: "",
			timezone: "America/New_York",
		});
		setUploadedFiles([]);
		setActualFiles([]);
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full overflow-y-auto px-8 py-9 sm:max-w-[832px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-lg font-semibold">
							Add New Location
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6 rounded-sm"
						>
							<X className="h-4 w-4" />
							<span className="sr-only">Close</span>
						</Button>
					</div>
				</SheetHeader>

				<div className="flex-1 space-y-3.5 pb-6">
					{/* Location Name */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Location name{" "}
							<span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter location name"
							value={formData.name || ""}
							onChange={(e) =>
								handleInputChange("name", e.target.value)
							}
							className="w-full"
							id="location-name"
							variant="default"
						/>
					</div>

					{/* Email and Phone */}
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Email
							</label>
							<InputText
								type="email"
								placeholder="Enter email"
								value={formData.email || ""}
								onChange={(e) =>
									handleInputChange("email", e.target.value)
								}
								className="w-full"
								id="email"
								variant="default"
							/>
						</div>
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Phone
							</label>
							<InputText
								type="tel"
								placeholder="Enter phone number"
								value={formData.phone || ""}
								onChange={(e) =>
									handleInputChange("phone", e.target.value)
								}
								className="w-full"
								id="phone"
								variant="default"
							/>
						</div>
					</div>

					{/* Address */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Address <span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter street address"
							value={formData.address?.street || ""}
							onChange={(e) =>
								handleInputChange(
									"address.street",
									e.target.value
								)
							}
							className="w-full"
							id="address"
							variant="default"
						/>
					</div>

					{/* Country */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Country
						</label>
						<Select
							value={formData.address?.country || ""}
							onValueChange={(value) =>
								handleInputChange(
									"address.country",
									value as string
								)
							}
						>
							<SelectTrigger className="w-full">
								<SelectValue placeholder="Select a country" />
							</SelectTrigger>
							<SelectContent>
								{countries.map((country) => (
									<SelectItem
										key={country.alpha2}
										value={country.alpha2}
									>
										{country.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Province and City */}
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Province
							</label>
							<InputText
								placeholder="Enter province/state"
								value={formData.address?.state || ""}
								onChange={(e) =>
									handleInputChange(
										"address.state",
										e.target.value
									)
								}
								className="w-full"
								id="province"
								variant="default"
							/>
						</div>
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								City
							</label>
							<InputText
								placeholder="Enter city"
								value={formData.address?.city || ""}
								onChange={(e) =>
									handleInputChange(
										"address.city",
										e.target.value
									)
								}
								className="w-full"
								id="city"
								variant="default"
							/>
						</div>
					</div>

					{/* Location Description */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Location description
						</label>
						<Textarea
							placeholder="Enter location description"
							value={formData.description || ""}
							onChange={(e) =>
								handleInputChange("description", e.target.value)
							}
							className="min-h-[120px] w-full resize-none"
						/>
					</div>

					{/* File Upload */}
					<div className="space-y-2">
						<Uploader
							files={uploadedFiles}
							onFilesChange={handleFilesChange}
							onFileRemove={handleFileRemove}
							onFileEdit={handleFileEdit}
							accept=".svg,.png,.jpg,.jpeg"
							maxFileSize={8 * 1024 * 1024} // 8MB
							uploadText="Click or drag file here to upload file"
							descriptionText="Recommended file type: .svg, .png, .jpg (Max of 8 mb)"
							multiple={true}
							maxFiles={5}
							size="default"
						/>
					</div>
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex-row justify-end gap-3 p-0">
					<Button
						variant="secondary"
						onClick={handleCancel}
						className="cursor-pointer"
					>
						Close
					</Button>
					<Button
						className="bg-primary hover:bg-primary/90 cursor-pointer"
						onClick={handleSubmit}
						disabled={
							isSubmitting ||
							!formData.name ||
							!formData.address?.street
						}
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
