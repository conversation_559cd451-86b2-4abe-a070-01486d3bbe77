import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface Toast {
	id: string;
	type: "success" | "error" | "warning" | "info";
	title: string;
	message?: string;
	duration?: number;
}

interface DrawerConfig {
	id: string;
	direction?: "top" | "bottom" | "left" | "right";
	size?: "sm" | "md" | "lg" | "xl" | "full";
	dismissible?: boolean;
	data: Record<string, any>;
	timestamp?: number;
}

interface ModalConfig {
	id: string;
	size?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full";
	dismissible?: boolean;
	data: Record<string, any>;
	timestamp?: number;
}

interface UIState {
	// Navigation
	sidebarCollapsed: boolean;
	mobileMenuOpen: boolean;
	expandedNavItem: string | null;
	activeTab: string;
	breadcrumbs: Array<{
		label: string;
		href?: string;
		isCurrentPage?: boolean;
	}>;
	showHeaderDate: boolean;

	// Modals & Overlays - Enhanced
	activeModal: string | null;
	modalData: Record<string, any>;
	modals: ModalConfig[];

	// Drawers - New
	activeDrawer: string | null;
	drawerData: Record<string, any>;
	drawers: DrawerConfig[];

	loadingStates: Record<string, boolean>;

	// Notifications
	toasts: Toast[];

	// Theme & Preferences
	theme: "light" | "dark" | "system";
	density: "comfortable" | "compact";

	// Performance tracking
	lastCleanup: number;

	// Actions
	toggleSidebar: () => void;
	setSidebarCollapsed: (collapsed: boolean) => void;
	setMobileMenuOpen: (open: boolean) => void;
	setExpandedNavItem: (itemId: string | null) => void;
	toggleNavItem: (itemId: string) => void;
	setActiveTab: (tab: string) => void;
	setBreadcrumbs: (breadcrumbs: UIState["breadcrumbs"]) => void;
	setShowHeaderDate: (show: boolean) => void;

	// Enhanced Modal Management
	openModal: (modalId: string, options?: Partial<ModalConfig>) => void;
	closeModal: (modalId?: string) => void;
	closeAllModals: () => void;

	// Drawer Management
	openDrawer: (drawerId: string, options?: Partial<DrawerConfig>) => void;
	closeDrawer: (drawerId?: string) => void;
	closeAllDrawers: () => void;

	// Performance optimizations
	cleanupOldModals: () => void;
	cleanupOldDrawers: () => void;
	performanceCleanup: () => void;

	setLoading: (key: string, loading: boolean) => void;

	addToast: (toast: Omit<Toast, "id">) => void;
	removeToast: (id: string) => void;
	clearToasts: () => void;

	setTheme: (theme: UIState["theme"]) => void;
	setDensity: (density: UIState["density"]) => void;
}

// Performance constants
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
const MAX_MODAL_HISTORY = 10;
const MAX_DRAWER_HISTORY = 10;

export const useUIStore = create<UIState>()(
	devtools(
		(set, get) => ({
			// Initial state
			sidebarCollapsed: false,
			mobileMenuOpen: false,
			expandedNavItem: null,
			activeTab: "overview",
			breadcrumbs: [],
			showHeaderDate: true,

			// Enhanced modal state
			activeModal: null,
			modalData: {},
			modals: [],

			// New drawer state
			activeDrawer: null,
			drawerData: {},
			drawers: [],

			loadingStates: {},
			toasts: [],
			theme: "system",
			density: "comfortable",
			lastCleanup: Date.now(),

			// Navigation actions
			toggleSidebar: () =>
				set((state) => {
					// On mobile, toggle the mobile menu
					// On desktop, toggle collapsed state
					const isMobile = window.innerWidth < 768;
					if (isMobile) {
						return { mobileMenuOpen: !state.mobileMenuOpen };
					} else {
						return { sidebarCollapsed: !state.sidebarCollapsed };
					}
				}),

			setSidebarCollapsed: (collapsed) =>
				set({ sidebarCollapsed: collapsed }),

			setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),

			setExpandedNavItem: (itemId) => set({ expandedNavItem: itemId }),

			toggleNavItem: (itemId) =>
				set((state) => ({
					expandedNavItem:
						state.expandedNavItem === itemId ? null : itemId,
				})),

			setActiveTab: (tab) => set({ activeTab: tab }),

			setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),

			setShowHeaderDate: (show) => set({ showHeaderDate: show }),

			// Enhanced Modal Management with Performance Optimization
			openModal: (modalId, options = {}) => {
				const modalConfig: ModalConfig = {
					id: modalId,
					size: options.size || "md",
					dismissible: options.dismissible ?? true,
					data: options.data || {},
					timestamp: Date.now(),
				};

				set((state) => {
					// Prevent unnecessary updates if modal is already active with same data
					if (
						state.activeModal === modalId &&
						JSON.stringify(state.modalData) ===
							JSON.stringify(modalConfig.data)
					) {
						return state;
					}

					// Auto cleanup if needed
					const shouldCleanup =
						Date.now() - state.lastCleanup > CLEANUP_INTERVAL;
					let updatedModals = state.modals;

					if (shouldCleanup) {
						updatedModals = state.modals.slice(-MAX_MODAL_HISTORY);
					}

					return {
						activeModal: modalId,
						modalData: modalConfig.data,
						modals: [
							...updatedModals.filter((m) => m.id !== modalId),
							modalConfig,
						],
						lastCleanup: shouldCleanup
							? Date.now()
							: state.lastCleanup,
					};
				});
			},

			closeModal: (modalId) => {
				if (modalId) {
					set((state) => ({
						modals: state.modals.filter((m) => m.id !== modalId),
						activeModal:
							state.activeModal === modalId
								? null
								: state.activeModal,
						modalData:
							state.activeModal === modalId
								? {}
								: state.modalData,
					}));
				} else {
					set({
						activeModal: null,
						modalData: {},
					});
				}
			},

			closeAllModals: () =>
				set({
					activeModal: null,
					modalData: {},
					modals: [],
				}),

			// Enhanced Drawer Management with Performance Optimization
			openDrawer: (drawerId, options = {}) => {
				const drawerConfig: DrawerConfig = {
					id: drawerId,
					direction: options.direction || "right",
					size: options.size || "md",
					dismissible: options.dismissible ?? true,
					data: options.data || {},
					timestamp: Date.now(),
				};

				set((state) => {
					// Auto cleanup if needed
					const shouldCleanup =
						Date.now() - state.lastCleanup > CLEANUP_INTERVAL;
					let updatedDrawers = state.drawers;

					if (shouldCleanup) {
						updatedDrawers =
							state.drawers.slice(-MAX_DRAWER_HISTORY);
					}

					return {
						activeDrawer: drawerId,
						drawerData: drawerConfig.data,
						drawers: [
							...updatedDrawers.filter((d) => d.id !== drawerId),
							drawerConfig,
						],
						lastCleanup: shouldCleanup
							? Date.now()
							: state.lastCleanup,
					};
				});
			},

			closeDrawer: (drawerId) => {
				if (drawerId) {
					set((state) => ({
						drawers: state.drawers.filter((d) => d.id !== drawerId),
						activeDrawer:
							state.activeDrawer === drawerId
								? null
								: state.activeDrawer,
						drawerData:
							state.activeDrawer === drawerId
								? {}
								: state.drawerData,
					}));
				} else {
					set({
						activeDrawer: null,
						drawerData: {},
					});
				}
			},

			closeAllDrawers: () =>
				set({
					activeDrawer: null,
					drawerData: {},
					drawers: [],
				}),

			// Performance optimization methods
			cleanupOldModals: () => {
				set((state) => {
					const cutoff = Date.now() - CLEANUP_INTERVAL;
					const activeModals = state.modals.filter(
						(modal) => modal.timestamp && modal.timestamp > cutoff
					);

					return {
						modals: activeModals.slice(-MAX_MODAL_HISTORY),
						lastCleanup: Date.now(),
					};
				});
			},

			cleanupOldDrawers: () => {
				set((state) => {
					const cutoff = Date.now() - CLEANUP_INTERVAL;
					const activeDrawers = state.drawers.filter(
						(drawer) =>
							drawer.timestamp && drawer.timestamp > cutoff
					);

					return {
						drawers: activeDrawers.slice(-MAX_DRAWER_HISTORY),
						lastCleanup: Date.now(),
					};
				});
			},

			performanceCleanup: () => {
				const state = get();
				if (Date.now() - state.lastCleanup > CLEANUP_INTERVAL) {
					state.cleanupOldModals();
					state.cleanupOldDrawers();
				}
			},

			// Loading states
			setLoading: (key, loading) =>
				set((state) => ({
					loadingStates: {
						...state.loadingStates,
						[key]: loading,
					},
				})),

			// Toast notifications
			addToast: (toast) => {
				const id = crypto.randomUUID();
				const newToast = { ...toast, id };

				set((state) => ({
					toasts: [...state.toasts, newToast],
				}));

				// Auto-remove toast after duration
				const duration = toast.duration ?? 5000;
				if (duration > 0) {
					setTimeout(() => {
						get().removeToast(id);
					}, duration);
				}
			},

			removeToast: (id) =>
				set((state) => ({
					toasts: state.toasts.filter((t) => t.id !== id),
				})),

			clearToasts: () => set({ toasts: [] }),

			// Theme actions
			setTheme: (theme) => set({ theme }),
			setDensity: (density) => set({ density }),
		}),
		{ name: "UIStore" }
	)
);

// Auto-cleanup interval setup
let cleanupInterval: NodeJS.Timeout | null = null;

export const startPerformanceCleanup = () => {
	if (cleanupInterval) clearInterval(cleanupInterval);

	cleanupInterval = setInterval(() => {
		const store = useUIStore.getState();
		store.performanceCleanup();
	}, CLEANUP_INTERVAL);
};

export const stopPerformanceCleanup = () => {
	if (cleanupInterval) {
		clearInterval(cleanupInterval);
		cleanupInterval = null;
	}
};
