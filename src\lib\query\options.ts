import type {
	UseQueryOptions,
	UseMutationOptions,
} from "@tanstack/react-query";

/**
 * Common query options that can be reused across different queries
 */

// Short-lived data (user interactions, real-time data)
export const shortLivedQueryOptions: Partial<UseQueryOptions> = {
	staleTime: 30 * 1000, // 30 seconds
	cacheTime: 2 * 60 * 1000, // 2 minutes
	refetchOnWindowFocus: true,
};

// Medium-lived data (user preferences, settings)
export const mediumLivedQueryOptions: Partial<UseQueryOptions> = {
	staleTime: 5 * 60 * 1000, // 5 minutes
	cacheTime: 10 * 60 * 1000, // 10 minutes
	refetchOnWindowFocus: false,
};

// Long-lived data (static configurations, rarely changing data)
export const longLivedQueryOptions: Partial<UseQueryOptions> = {
	staleTime: 60 * 60 * 1000, // 1 hour
	cacheTime: 2 * 60 * 60 * 1000, // 2 hours
	refetchOnWindowFocus: false,
};

// Background refresh data (periodic updates without user interaction)
export const backgroundRefreshOptions: Partial<UseQueryOptions> = {
	staleTime: 2 * 60 * 1000, // 2 minutes
	cacheTime: 5 * 60 * 1000, // 5 minutes
	refetchInterval: 5 * 60 * 1000, // 5 minutes
	refetchOnWindowFocus: false,
};

// Critical data that should always be fresh
export const criticalDataOptions: Partial<UseQueryOptions> = {
	staleTime: 0, // Always stale
	cacheTime: 60 * 1000, // 1 minute
	refetchOnWindowFocus: true,
	refetchOnMount: true,
};

/**
 * Common mutation options
 */
export const defaultMutationOptions: Partial<UseMutationOptions> = {
	retry: 1,
	retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

export const criticalMutationOptions: Partial<UseMutationOptions> = {
	retry: 3,
	retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
};
