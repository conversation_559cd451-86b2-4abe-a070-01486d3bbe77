import * as React from "react";
import { cn } from "@/lib/utils";
import { Star, ThumbsUp } from "lucide-react";

export interface MetricData {
	value: string;
	label: string;
	percentage: number;
	primaryColor: string;
	secondaryColor: string;
	primaryLabel: string;
	secondaryLabel: string;
}

export interface FeedbackCardProps
	extends React.HTMLAttributes<HTMLDivElement> {
	rating: number;
	reviewCount: number;
	metrics: MetricData[];
	variant?: "default" | "compact";
}

const FeedbackCard = React.forwardRef<HTMLDivElement, FeedbackCardProps>(
	(
		{
			className,
			rating,
			reviewCount,
			metrics,
			variant = "default",
			...props
		},
		ref
	) => {
		const CircularProgress = ({
			percentage,
			primaryColor,
			// secondaryColor,
		}: {
			percentage: number;
			primaryColor: string;
			secondaryColor: string;
		}) => {
			const radius = 20;
			const circumference = 2 * Math.PI * radius;
			const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;

			return (
				<div className="relative h-12 w-12">
					<svg className="h-12 w-12 -rotate-90" viewBox="0 0 48 48">
						{/* Background circle */}
						<circle
							cx="24"
							cy="24"
							r={radius}
							stroke="#E5E7EB"
							strokeWidth="4"
							fill="none"
						/>
						{/* Progress circle */}
						<circle
							cx="24"
							cy="24"
							r={radius}
							stroke={primaryColor}
							strokeWidth="4"
							fill="none"
							strokeDasharray={strokeDasharray}
							strokeLinecap="round"
							className="transition-all duration-300 ease-in-out"
						/>
					</svg>
				</div>
			);
		};

		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex h-36 w-96 items-center justify-start gap-1 rounded-lg bg-[#FAFAFA] p-1",
					variant === "compact" && "h-32 w-80",
					className
				)}
				{...props}
			>
				<div className="flex items-center justify-center gap-4 self-stretch border-r border-[#E4E4E7] p-5">
					<div className="inline-flex flex-col items-center justify-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center">
							<Star className="h-10 w-10 fill-amber-500 text-amber-500" />
						</div>

						<div className="flex flex-col items-center justify-center gap-1">
							<div className="text-center">
								<span className="text-lg leading-7 font-semibold text-gray-900">
									{rating.toFixed(1)}{" "}
								</span>
								<span className="text-[10px] leading-3 font-normal text-gray-500">
									Average Rating
								</span>
							</div>
							<div className="inline-flex items-center justify-center gap-1">
								<ThumbsUp className="h-3 w-3  text-amber-500" />
								<div className="text-center">
									<span className="text-[10px] leading-3 font-bold text-gray-900">
										{reviewCount.toLocaleString()}
									</span>
									<span className="text-[10px] leading-3 font-normal text-gray-900">
										{" "}
										Reviews
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div className="inline-flex flex-1 flex-col items-start justify-center self-stretch">
					{metrics.map((metric, index) => (
						<div
							key={index}
							className={cn(
								"inline-flex flex-1 items-center justify-start gap-4 self-stretch pl-4",
								index > 0 && "border-t border-[#E4E4E7]"
							)}
						>
							<CircularProgress
								percentage={metric.percentage}
								primaryColor={metric.primaryColor}
								secondaryColor={metric.secondaryColor}
							/>

							<div className="inline-flex flex-col items-start justify-start gap-px">
								<div className="self-stretch">
									<span className="text-lg leading-7 font-semibold text-gray-900">
										{metric.value}{" "}
									</span>
									<span className="text-[10px] leading-3 font-normal text-gray-500">
										{metric.label}
									</span>
								</div>
								<div className="inline-flex items-center justify-start gap-4 self-stretch">
									<div className="flex items-center justify-start gap-1">
										<div
											className="h-2 w-2 rounded-[2px]"
											style={{
												backgroundColor:
													metric.primaryColor,
											}}
										/>
										<div className="text-[8px] leading-3 font-normal text-zinc-950">
											{metric.primaryLabel}
										</div>
									</div>
									<div className="flex items-center justify-start gap-1">
										<div
											className="h-2 w-2 rounded-[2px]"
											style={{
												backgroundColor:
													metric.secondaryColor,
											}}
										/>
										<div className="text-[8px] leading-3 font-normal text-zinc-950">
											{metric.secondaryLabel}
										</div>
									</div>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		);
	}
);

FeedbackCard.displayName = "FeedbackCard";

export { FeedbackCard };
