import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";

export interface TabItem {
	value: string;
	label: string;
	count?: number;
	disabled?: boolean;
}

export interface TabsProps
	extends Omit<React.ComponentProps<typeof TabsPrimitive.Root>, "children"> {
	items: TabItem[];
	children?: React.ReactNode;
	className?: string;
	// Routing props
	useRouting?: boolean;
	searchParamKey?: string;
	defaultTab?: string;
}

export interface TabsTriggerProps
	extends React.ComponentProps<typeof TabsPrimitive.Trigger> {
	count?: number;
	className?: string;
}

export interface TabsListProps
	extends React.ComponentProps<typeof TabsPrimitive.List> {
	className?: string;
}

export interface TabsContentProps
	extends React.ComponentProps<typeof TabsPrimitive.Content> {
	className?: string;
}
