import * as React from "react";
import { cn } from "@/lib/utils";
import { Star } from "lucide-react";

export interface StarRating {
	stars: number;
	count: number;
	percentage?: number;
}

export interface StarCardProps extends React.HTMLAttributes<HTMLDivElement> {
	ratings: StarRating[];
	variant?: "default" | "compact";
	showStars?: boolean;
	barColor?: string;
	backgroundColor?: string;
}

const StarCard = React.forwardRef<HTMLDivElement, StarCardProps>(
	(
		{
			className,
			ratings,
			variant = "default",
			showStars = true,
			barColor = "bg-[#FF9500]",
			backgroundColor = "bg-[#E4E4E7]",
			...props
		},
		ref
	) => {
		const totalCount = ratings.reduce(
			(sum, rating) => sum + rating.count,
			0
		);
		const processedRatings = ratings.map((rating) => ({
			...rating,
			percentage:
				rating.percentage ||
				(totalCount > 0 ? (rating.count / totalCount) * 100 : 0),
		}));

		const renderStars = (count: number) => {
			return Array.from({ length: count }, (_, i) => (
				<Star
					key={i}
					className="h-3 w-3 "
				/>
			));
		};

		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex w-full max-w-[756px] flex-col items-center justify-center gap-2 overflow-hidden rounded-xl bg-[#FAFAFA] p-3",
					variant === "compact" && "gap-1 p-2",
					className
				)}
				{...props}
			>
				<div className="flex flex-col items-start justify-start gap-3 self-stretch">
					{processedRatings.map((rating, index) => (
						<div
							key={index}
							className="inline-flex items-center justify-start gap-3 self-stretch"
						>
							{showStars && (
								<div className="flex items-center justify-start gap-0.5">
									{renderStars(rating.stars)}
								</div>
							)}
							<div
								className={cn(
									"inline-flex h-3 flex-1 flex-col items-start justify-start gap-2.5 overflow-hidden rounded-[10px] p-0.5",
									backgroundColor
								)}
							>
								<div
									className={cn(
										"flex-1 rounded-[20px]",
										barColor
									)}
									style={{
										width: `${Math.min(rating.percentage, 100)}%`,
									}}
								/>
							</div>
							<div className="text-base w-4 justify-center text-right  text-[10px] leading-3 font-normal">
								{rating.count}
							</div>
						</div>
					))}
				</div>
			</div>
		);
	}
);

StarCard.displayName = "StarCard";

export { StarCard };
