import { useState, type FC } from "react";
import { MapPin } from "lucide-react";
import { useLocations } from "../hooks";
import type { LocationsFilters } from "../types";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { LocationTab } from "./LocationTab";

export interface LocationsListProps {
	className?: string;
}

export const LocationsList: FC<LocationsListProps> = ({ className }) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});

	const { error } = useLocations(filters);

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<MapPin className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						Error loading locations
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						{error instanceof Error
							? error.message
							: "Something went wrong"}
					</p>
				</div>
			</div>
		);
	}

	const items = [
		{ value: "locations", label: "Locations" },
		{ value: "services", label: "Services" },
		{ value: "team-members", label: "Team Members" },
		{ value: "schedule-settings", label: "Schedule Settings" },
		{ value: "waitlist-settings", label: "Waitlist Settings" },
		{ value: "organization-settings", label: "Organization Settings" },
	];

	return (
		<div className={cn("flex flex-col gap-0.5 p-2", className)}>
			{/* Tabs */}
			<Tabs
				items={items}
				defaultValue="locations"
				useRouting={true}
				searchParamKey="location-tab"
			>
				<TabsContent value="locations">
					<LocationTab />
				</TabsContent>
				<TabsContent value="services">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Services Content
						</h3>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
};
