export interface Location {
	id: string;
	name: string;
	address: {
		street: string;
		city: string;
		state: string;
		zipCode: string;
		country: string;
	};
	phone?: string;
	email?: string;
	description?: string;
	isActive: boolean;
	timezone: string;
	coordinates?: {
		latitude: number;
		longitude: number;
	};
	operatingHours?: OperatingHours[];
	services?: string[];
	capacity?: number;
	amenities?: string[];
	organizationId: string;
	createdAt: string;
	updatedAt: string;
}

export interface OperatingHours {
	dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
	openTime: string; // "09:00"
	closeTime: string; // "17:00"
	isClosed: boolean;
}

export interface CreateLocationRequest {
	name: string;
	address: Location["address"];
	phone?: string;
	email?: string;
	description?: string;
	timezone: string;
	coordinates?: Location["coordinates"];
	operatingHours?: OperatingHours[];
	services?: string[];
	capacity?: number;
	amenities?: string[];
}

export interface UpdateLocationRequest extends Partial<CreateLocationRequest> {
	id: string;
	isActive?: boolean;
}

export interface LocationsFilters {
	search?: string;
	isActive?: boolean;
	city?: string;
	state?: string;
	services?: string[];
	page?: number;
	limit?: number;
	sortBy?: "name" | "city" | "createdAt" | "updatedAt";
	sortOrder?: "asc" | "desc";
}

export interface LocationsResponse {
	data: Location[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}
