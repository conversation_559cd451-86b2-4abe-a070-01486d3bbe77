import { useUIStore } from "@/stores/uiStore";
import { useEffect, useCallback, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, Settings, Download, Plus, FolderOpen } from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import {
	AllPatientListCard,
	type Patient,
} from "@/components/ui-components/AllPatientListCard";

export default function Clients() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);

	// Mock patients data
	const [patients] = useState<Patient[]>([
		{
			id: "1",
			name: "Dr. <PERSON>",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "12 Aug 2024",
			syncStatus: "failed",
		},
		{
			id: "2",
			name: "Dr. <PERSON>",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "15 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "3",
			name: "Dr. <PERSON> <PERSON>",
			status: "Inactive",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "10 Aug 2024",
			syncStatus: "pending",
		},
		{
			id: "4",
			name: "Dr. Emily Rodriguez",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "18 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "5",
			name: "Dr. James Wilson",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "20 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "6",
			name: "Dr. Lisa Anderson",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "22 Aug 2024",
			syncStatus: "synced",
		},
	]);

	const [hasPatients, setHasPatients] = useState(true);

	const handleSearch = useCallback(() => {
		// TODO: Implement search functionality
		console.log("Search clicked");
	}, []);

	const handleSettings = useCallback(() => {
		// TODO: Implement settings/filter functionality
		console.log("Settings clicked");
	}, []);

	const handleImportCSV = useCallback(() => {
		// TODO: Implement CSV import functionality
		console.log("Import CSV clicked");
	}, []);

	const handleAddPatient = useCallback(() => {
		// TODO: Implement add patient functionality
		console.log("Add Patient clicked");
		// For demo purposes, toggle to show patient list
		setHasPatients(true);
	}, []);

	// Patient action handlers
	const handleEditPatient = useCallback((patient: Patient) => {
		console.log("Edit patient:", patient.id);
		// TODO: Implement edit patient functionality
	}, []);

	const handleDeletePatient = useCallback((patient: Patient) => {
		console.log("Delete patient:", patient.id);
		// TODO: Implement delete patient functionality
	}, []);

	const handleCallPatient = useCallback((patient: Patient) => {
		console.log("Call patient:", patient.phone);
		// TODO: Implement call patient functionality
	}, []);

	const handleEmailPatient = useCallback((patient: Patient) => {
		console.log("Email patient:", patient.email);
		// TODO: Implement email patient functionality
	}, []);

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/clients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		// Set the custom header content
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				{/* Left side - Title */}
				<h1 className="text-foreground text-2xl font-bold">
					List of Patients
				</h1>

				{/* Right side - Action buttons */}
				<div className="flex items-center space-x-3">
					{/* Search Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSearch}
						className="h-9 w-9"
					>
						<Search className="h-4 w-4" />
					</Button>

					{/* Settings/Filter Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>

					{/* Import CSV Button */}
					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Download className="h-4 w-4" />
						Import CSV
					</Button>

					{/* Add Patient Button */}
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [
		setPageHeaderContent,
		handleSearch,
		handleSettings,
		handleImportCSV,
		handleAddPatient,
	]);

	return (
		<div className="space-y-6">
			{!hasPatients ? (
				// Empty state when no patients
				<div className="flex min-h-[400px] items-center justify-center">
					<EmptyContent
						title="No patients added"
						description="Select file or Drag and drop here to import list CSV files up to 50 MB are accepted, or via Google Sheets and Microsoft Excel."
						actions={[
							{
								label: "Add New",
								onClick: handleAddPatient,
								variant: "primary",
							},
							{
								label: "Import",
								onClick: handleImportCSV,
								variant: "outline",
							},
						]}
					/>
				</div>
			) : (
				<div className="border mt-2">
					<div className="flex flex-1 items-center justify-between pl-5 py-[15.5px]">
						<p className="text-main-1 w-[19%] text-sm text-[14px] font-bold">
							Name
						</p>
						<p className="text-main-1 w-[10%] text-sm text-[14px] font-bold">
							Status
						</p>
						<p className="text-main-1 w-[19%] text-sm text-[14px] font-bold">
							Email
						</p>
						<p className="text-main-1 w-[16%] text-sm text-[14px] font-bold">
							Phone
						</p>
						<p className="text-main-1 w-[16%] text-sm text-[14px] font-bold">
							Last Visit
						</p>
					</div>
					<div className="bg-gray-50">
						{patients.map((patient) => (
							<AllPatientListCard
								key={patient.id}
								patient={patient}
								actions={[
									{
										type: "edit",
										onClick: handleEditPatient,
									},
									{
										type: "call",
										onClick: handleCallPatient,
									},
									{
										type: "email",
										onClick: handleEmailPatient,
									},
									{
										type: "delete",
										onClick: handleDeletePatient,
									},
								]}
							/>
						))}
					</div>
				</div>
			)}
		</div>
	);
}
