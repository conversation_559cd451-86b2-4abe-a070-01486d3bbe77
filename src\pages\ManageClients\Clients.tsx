import { useUIStore } from "@/stores/uiStore";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Search, Settings, Download, Plus } from "lucide-react";

export default function Clients() {
	const { setBreadcrumbs, setPageHeaderContent } = useUIStore((state) => ({
		setBreadcrumbs: state.setBreadcrumbs,
		setPageHeaderContent: state.setPageHeaderContent,
	}));

	const handleSearch = () => {
		// TODO: Implement search functionality
		console.log("Search clicked");
	};

	const handleSettings = () => {
		// TODO: Implement settings/filter functionality
		console.log("Settings clicked");
	};

	const handleImportCSV = () => {
		// TODO: Implement CSV import functionality
		console.log("Import CSV clicked");
	};

	const handleAddPatient = () => {
		// TODO: Implement add patient functionality
		console.log("Add Patient clicked");
	};

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/clients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		// Set the custom header content
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				{/* Left side - Title */}
				<h1 className="text-foreground text-2xl font-bold">
					List of Patients
				</h1>

				{/* Right side - Action buttons */}
				<div className="flex items-center space-x-3">
					{/* Search Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSearch}
						className="h-9 w-9"
					>
						<Search className="h-4 w-4" />
					</Button>

					{/* Settings/Filter Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>

					{/* Import CSV Button */}
					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Download className="h-4 w-4" />
						Import CSV
					</Button>

					{/* Add Patient Button */}
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [setPageHeaderContent]);

	return <div className="space-y-6">{/* Content will go here */}</div>;
}
