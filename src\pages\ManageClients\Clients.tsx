import { useUIStore } from "@/stores/uiStore";
import { useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Search, Settings, Download, Plus, Settings2, RefreshCcw } from "lucide-react";

export default function Clients() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);

	const handleSearch = useCallback(() => {
		// TODO: Implement search functionality
		console.log("Search clicked");
	}, []);

	const handleSettings = useCallback(() => {
		// TODO: Implement settings/filter functionality
		console.log("Settings clicked");
	}, []);

	const handleImportCSV = useCallback(() => {
		// TODO: Implement CSV import functionality
		console.log("Import CSV clicked");
	}, []);

	const handleAddPatient = useCallback(() => {
		// TODO: Implement add patient functionality
		console.log("Add Patient clicked");
	}, []);

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/clients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		// Set the custom header content
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				{/* Left side - Title */}
				<h1 className="text-foreground text-2xl font-bold">
					List of Patients
				</h1>

				{/* Right side - Action buttons */}
				<div className="flex items-center space-x-3">
					{/* Search Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSearch}
						className="h-9 w-9"
					>
						<Download className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={handleSearch}
						className="h-9 w-9"
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					{/* Settings/Filter Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={handleSearch}
						className="h-9 w-9"
					>
						<Search className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="cursor-pointer"
						onClick={() => window.location.reload()}
					>
						<RefreshCcw className="text-muted-foreground h-4 w-4" />
					</Button>

					{/* Import CSV Button */}
					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Download className="h-4 w-4" />
						Import CSV
					</Button>

					{/* Add Patient Button */}
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [
		setPageHeaderContent,
		handleSearch,
		handleSettings,
		handleImportCSV,
		handleAddPatient,
	]);

	return <div className="space-y-6">{/* Content will go here */}</div>;
}
