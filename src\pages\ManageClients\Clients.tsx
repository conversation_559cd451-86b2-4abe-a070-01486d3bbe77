import { useUIStore } from "@/stores/uiStore";
import { useEffect } from "react";

export default function Clients() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/clients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between py-3.5">
				<div className="flex items-center space-x-2">
					<h1 className="text-base-foreground justify-start font-['Inter'] text-xl leading-9 font-bold">
						Clients
					</h1>
				</div>
			</div>
		</div>
	);
}
