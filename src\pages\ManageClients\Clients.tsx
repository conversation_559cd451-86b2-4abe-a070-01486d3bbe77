import { useUIStore } from "@/stores/uiStore";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Search, Settings, Download, Plus } from "lucide-react";

export default function Clients() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/clients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	const handleSearch = () => {
		// TODO: Implement search functionality
		console.log("Search clicked");
	};

	const handleSettings = () => {
		// TODO: Implement settings/filter functionality
		console.log("Settings clicked");
	};

	const handleImportCSV = () => {
		// TODO: Implement CSV import functionality
		console.log("Import CSV clicked");
	};

	const handleAddPatient = () => {
		// TODO: Implement add patient functionality
		console.log("Add Patient clicked");
	};

	return (
		<div className="space-y-6">
			{/* Page Header */}
			<div className="flex items-center justify-between py-3.5">
				{/* Left side - Title */}
				<div className="flex items-center space-x-2">
					<h1 className="text-base-foreground justify-start font-['Inter'] text-xl leading-9 font-bold">
						List of Patients
					</h1>
				</div>

				{/* Right side - Action buttons */}
				<div className="flex items-center space-x-3">
					{/* Search Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSearch}
						className="h-9 w-9"
					>
						<Search className="h-4 w-4" />
					</Button>

					{/* Settings/Filter Button */}
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>

					{/* Import CSV Button */}
					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Download className="h-4 w-4" />
						Import CSV
					</Button>

					{/* Add Patient Button */}
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		</div>
	);
}
