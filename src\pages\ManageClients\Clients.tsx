import { useUIS<PERSON> } from "@/stores/uiStore";
import { useEffect, useCallback, useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Settings, Download, Plus, FolderOpen, Settings2, RefreshCcw, Upload } from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import {
	AllPatientListCard,
	type Patient,
} from "@/components/ui-components/AllPatientListCard";
import { Checkbox } from "@/components/common/Checkbox";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";

export default function Clients() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);
	const [patients] = useState<Patient[]>([
		{
			id: "1",
			name: "<PERSON><PERSON> <PERSON>",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "12 Aug 2024",
			syncStatus: "failed",
		},
		{
			id: "2",
			name: "Dr. Sarah <PERSON>",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "15 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "3",
			name: "Dr. Michael Chen",
			status: "Inactive",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "10 Aug 2024",
			syncStatus: "pending",
		},
		{
			id: "4",
			name: "Dr. Emily Rodriguez",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "18 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "5",
			name: "Dr. James Wilson",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "20 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "6",
			name: "Dr. Lisa Anderson",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "22 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "7",
			name: "Dr. Robert Taylor",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "25 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "8",
			name: "Dr. Maria Garcia",
			status: "Inactive",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "20 Aug 2024",
			syncStatus: "pending",
		},
		{
			id: "9",
			name: "Dr. David Brown",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "28 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "10",
			name: "Dr. Jennifer Davis",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "30 Aug 2024",
			syncStatus: "synced",
		},
		{
			id: "11",
			name: "Dr. Christopher Miller",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "02 Sep 2024",
			syncStatus: "failed",
		},
		{
			id: "12",
			name: "Dr. Amanda Wilson",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "05 Sep 2024",
			syncStatus: "synced",
		},
		{
			id: "13",
			name: "Dr. Kevin Moore",
			status: "Inactive",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "01 Sep 2024",
			syncStatus: "pending",
		},
		{
			id: "14",
			name: "Dr. Michelle Thompson",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "08 Sep 2024",
			syncStatus: "synced",
		},
		{
			id: "15",
			name: "Dr. Daniel White",
			status: "Active",
			email: "<EMAIL>",
			phone: "+****************",
			lastVisit: "10 Sep 2024",
			syncStatus: "synced",
		},
	]);

	const [hasPatients, setHasPatients] = useState(true);
	const [selectedPatients, setSelectedPatients] = useState<string[]>([]);
	const [currentPage, setCurrentPage] = useState(1);
	const patientsPerPage = 7;

	const totalPages = Math.ceil(patients.length / patientsPerPage);
	const startIndex = (currentPage - 1) * patientsPerPage;
	const endIndex = startIndex + patientsPerPage;
	const currentPatients = useMemo(
		() => patients.slice(startIndex, endIndex),
		[patients, startIndex, endIndex]
	);

	const handleSearch = useCallback(() => {
		// TODO: Implement search functionality
		console.log("Search clicked");
	}, []);

	const handleSettings = useCallback(() => {
		// TODO: Implement settings/filter functionality
		console.log("Settings clicked");
	}, []);

	const handleImportCSV = useCallback(() => {
		// TODO: Implement CSV import functionality
		console.log("Import CSV clicked");
	}, []);

	const handleAddPatient = useCallback(() => {
		// TODO: Implement add patient functionality
		console.log("Add Patient clicked");
		// For demo purposes, toggle to show patient list
		setHasPatients(true);
		// Clear any existing selections when showing patient list
		setSelectedPatients([]);
		// Reset to page 1 when patients are added
		setCurrentPage(1);
	}, []);

	// Pagination handlers
	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
		// Clear selections when changing pages (optional - you can maintain selections if preferred)
		setSelectedPatients([]);
		console.log("Changed to page:", page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	// Patient action handlers
	const handleEditPatient = useCallback((patient: Patient) => {
		console.log("Edit patient:", patient.id);
		// TODO: Implement edit patient functionality
	}, []);

	const handleDeletePatient = useCallback((patient: Patient) => {
		console.log("Delete patient:", patient.id);
		// TODO: Implement delete patient functionality
	}, []);

	const handleCallPatient = useCallback((patient: Patient) => {
		console.log("Call patient:", patient.phone);
		// TODO: Implement call patient functionality
	}, []);

	const handleEmailPatient = useCallback((patient: Patient) => {
		console.log("Email patient:", patient.email);
		// TODO: Implement email patient functionality
	}, []);

	const handleSelectAll = useCallback(
		(checked: boolean) => {
			if (checked) {
				const currentPagePatientIds = currentPatients.map(
					(patient) => patient.id
				);
				setSelectedPatients((prev) => {
					const newSelections = [
						...prev,
						...currentPagePatientIds.filter(
							(id) => !prev.includes(id)
						),
					];
					console.log(
						"Selected all patients on current page:",
						currentPagePatientIds
					);
					return newSelections;
				});
			} else {
				const currentPagePatientIds = currentPatients.map(
					(patient) => patient.id
				);
				setSelectedPatients((prev) => {
					const newSelections = prev.filter(
						(id) => !currentPagePatientIds.includes(id)
					);
					console.log(
						"Deselected all patients on current page:",
						currentPagePatientIds
					);
					return newSelections;
				});
			}
		},
		[currentPatients]
	);

	const handlePatientSelection = useCallback(
		(patientId: string, selected: boolean) => {
			if (selected) {
				setSelectedPatients((prev) => {
					const newSelection = [...prev, patientId];
					console.log(
						"Selected patient:",
						patientId,
						"Total selected:",
						newSelection.length
					);
					return newSelection;
				});
			} else {
				setSelectedPatients((prev) => {
					const newSelection = prev.filter((id) => id !== patientId);
					console.log(
						"Deselected patient:",
						patientId,
						"Total selected:",
						newSelection.length
					);
					return newSelection;
				});
			}
		},
		[]
	);

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/clients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<h1 className="text-foreground text-2xl font-bold">
					List of Patients
				</h1>
				<div className="flex items-center space-x-3">
					<Button variant="outline" size="icon" className="h-9 w-9">
						<Download className="h-4 w-4" />
					</Button>
					<Button variant="outline" size="icon" className="h-9 w-9">
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
					>
						<RefreshCcw className="h-4 w-4" />
					</Button>

					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Upload className="h-4 w-4" />
						Import CSV
					</Button>
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [
		setPageHeaderContent,
		handleSearch,
		handleSettings,
		handleImportCSV,
		handleAddPatient,
	]);

	return (
		<div className="space-y-">
			{!hasPatients ? (
				<div className="flex min-h-[400px] items-center justify-center">
					<EmptyContent
						title="No patients added"
						description="Select file or Drag and drop here to import list CSV files up to 50 MB are accepted, or via Google Sheets and Microsoft Excel."
						actions={[
							{
								label: "Add New",
								onClick: handleAddPatient,
								variant: "primary",
							},
							{
								label: "Import",
								onClick: handleImportCSV,
								variant: "outline",
							},
						]}
					/>
				</div>
			) : (
				<div>
					<div className="mt-2 rounded-xl border border-[#E4E4E7]">
						<div className="flex items-center border-b border-gray-200 py-[15.5px]">
							<div className="flex items-center px-4">
								<Checkbox
									checked={
										currentPatients.length > 0 &&
										currentPatients.every((patient) =>
											selectedPatients.includes(
												patient.id
											)
										)
									}
									onCheckedChange={handleSelectAll}
									className="border-[#005893]"
								/>
							</div>

							<div className="flex w-72 min-w-20 items-center gap-3 px-3">
								<div className="flex items-center gap-2">
									<p className="text-main-1 text-sm text-[14px] font-bold">
										Name
									</p>
									{selectedPatients.length > 0 && (
										<span className="text-xs text-gray-500">
											({selectedPatients.length} selected
											total)
										</span>
									)}
								</div>
							</div>

							<div className="flex w-32 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Status
								</p>
							</div>

							<div className="flex w-60 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Email
								</p>
							</div>

							<div className="flex w-40 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Phone
								</p>
							</div>
							<div className="flex w-36 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Last Visit
								</p>
							</div>
						</div>

						{currentPatients.map((patient) => (
							<AllPatientListCard
								key={patient.id}
								patient={patient}
								checked={selectedPatients.includes(patient.id)}
								onCheckboxChange={handlePatientSelection}
								actions={[
									{
										type: "edit",
										onClick: handleEditPatient,
									},
									{
										type: "call",
										onClick: handleCallPatient,
									},
									{
										type: "email",
										onClick: handleEmailPatient,
									},
									{
										type: "delete",
										onClick: handleDeletePatient,
									},
								]}
							/>
						))}
					</div>
				</div>
			)}
			{totalPages > 1 && (
				<div className="mt-2 flex justify-end">
					<div>
						<Pagination>
							<PaginationContent>
								<PaginationItem>
									<PaginationPrevious
										onClick={handlePreviousPage}
										className={
											currentPage === 1
												? "pointer-events-none opacity-50"
												: "cursor-pointer"
										}
									/>
								</PaginationItem>
								{Array.from(
									{ length: totalPages },
									(_, i) => i + 1
								).map((page) => (
									<PaginationItem key={page}>
										<PaginationLink
											onClick={() =>
												handlePageChange(page)
											}
											isActive={currentPage === page}
											className="cursor-pointer"
										>
											{page}
										</PaginationLink>
									</PaginationItem>
								))}

								{totalPages > 5 &&
									currentPage < totalPages - 2 && (
										<PaginationItem>
											<PaginationEllipsis />
										</PaginationItem>
									)}

								<PaginationItem>
									<PaginationNext
										onClick={handleNextPage}
										className={
											currentPage === totalPages
												? "pointer-events-none opacity-50"
												: "cursor-pointer"
										}
									/>
								</PaginationItem>
							</PaginationContent>
						</Pagination>
					</div>
				</div>
			)}
		</div>
	);
}
