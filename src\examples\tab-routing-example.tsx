import React from "react";
import { Link } from "react-router";
import { Tabs, TabsContent, useTabNavigation } from "@/components/common/Tabs";

export function TabRoutingExample() {
	const { currentTab, navigateToTab } = useTabNavigation({
		searchParamKey: "tab",
		defaultTab: "overview",
	});

	const tabItems = [
		{ value: "overview", label: "Overview", count: 5 },
		{ value: "settings", label: "Settings" },
		{ value: "analytics", label: "Analytics", count: 23 },
		{ value: "billing", label: "Billing", disabled: true },
	];

	return (
		<div className="p-6">
			<h1 className="mb-4 text-2xl font-bold">Tab Routing Example</h1>

			{/* Example of programmatic navigation */}
			<div className="mb-4 space-x-2">
				<button
					onClick={() => navigateToTab("overview")}
					className="rounded bg-blue-500 px-3 py-1 text-white"
				>
					Go to Overview
				</button>
				<button
					onClick={() => navigateToTab("analytics")}
					className="rounded bg-green-500 px-3 py-1 text-white"
				>
					Go to Analytics
				</button>
			</div>

			{/* Example of direct links */}
			<div className="mb-4 space-x-2">
				<Link to="?tab=overview" className="text-blue-600 underline">
					Overview Link
				</Link>
				<Link to="?tab=settings" className="text-blue-600 underline">
					Settings Link
				</Link>
				<Link to="?tab=analytics" className="text-blue-600 underline">
					Analytics Link
				</Link>
			</div>

			<p className="mb-4 text-sm text-gray-600">
				Current tab: <strong>{currentTab}</strong>
			</p>

			{/* The routed tabs */}
			<Tabs
				items={tabItems}
				useRouting={true}
				searchParamKey="tab"
				defaultTab="overview"
				className="w-full"
			>
				<TabsContent value="overview">
					<div className="rounded bg-gray-50 p-4">
						<h2 className="text-lg font-semibold">
							Overview Content
						</h2>
						<p>
							This is the overview tab content. URL: ?tab=overview
						</p>
					</div>
				</TabsContent>

				<TabsContent value="settings">
					<div className="rounded bg-gray-50 p-4">
						<h2 className="text-lg font-semibold">
							Settings Content
						</h2>
						<p>
							This is the settings tab content. URL: ?tab=settings
						</p>
					</div>
				</TabsContent>

				<TabsContent value="analytics">
					<div className="rounded bg-gray-50 p-4">
						<h2 className="text-lg font-semibold">
							Analytics Content
						</h2>
						<p>
							This is the analytics tab content. URL:
							?tab=analytics
						</p>
					</div>
				</TabsContent>

				<TabsContent value="billing">
					<div className="rounded bg-gray-50 p-4">
						<h2 className="text-lg font-semibold">
							Billing Content
						</h2>
						<p>
							This is the billing tab content. URL: ?tab=billing
						</p>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
