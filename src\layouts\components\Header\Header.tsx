import { cn } from "@/lib/utils";
import { Breadcrumb } from "@/components/common/Breadcrumb";
import type { HeaderProps } from "./types";
import { Bell, Search, PanelLeft, PanelRight, RefreshCcw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useUIStore } from "@/stores/uiStore";
import { useAuthStore } from "@/stores/authStore";
import { Calendar } from "lucide-react";
import dayjs from "dayjs";

export const Header = ({
	className,
	showUserMenu = true,
	notificationCount = 2,
	userAvatar = "https://placehold.co/40x40",
	...props
}: HeaderProps) => {
	const { toggleSidebar, sidebarCollapsed, showHeaderDate, breadcrumbs } =
		useUIStore();
	const { selectedOrganisation } = useAuthStore();
	return (
		<header
			className={cn(
				"bg-background border-border inline-flex items-center justify-between self-stretch border-b p-6 pb-3 shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]",
				className
			)}
			{...props}
		>
			<div className="flex flex-1 flex-col items-center gap-4 self-stretch">
				<div className="flex flex-1 items-center justify-between self-stretch">
					{/* Left side - Logo and Breadcrumb */}
					<div className="flex items-center justify-start gap-4">
						{/* Logo/Icon */}
						<Button
							variant="ghost"
							size="icon"
							onClick={toggleSidebar}
							className="flex size-7 cursor-pointer items-center justify-center"
						>
							{sidebarCollapsed ? (
								<PanelRight className="text-foreground h-4 w-4" />
							) : (
								<PanelLeft className="text-foreground h-4 w-4" />
							)}
						</Button>

						{/* Divider */}
						<div className="flex w-0.5 items-center justify-start">
							<div className="relative h-3.5 w-0">
								<div className="outline-border absolute top-0 left-0 h-3.5 w-0 outline-1 outline-offset-[-0.50px]" />
							</div>
						</div>

						{/* Breadcrumb Navigation */}
						<Breadcrumb
							items={breadcrumbs}
							variant="default"
							size="md"
							className="flex flex-wrap content-center items-center justify-start gap-2.5"
						/>
					</div>

					{/* Right side - Actions and User Menu */}
					{showUserMenu && (
						<div className="flex w-80 items-center justify-end gap-6">
							<div className="flex items-center justify-start gap-4">
								{/* Search Icon */}
								<Button
									asChild
									variant="ghost"
									size="icon"
									className="cursor-pointer"
								>
									<Search className="text-muted-foreground h-4 w-4" />
								</Button>

								{/* Notification Button with Badge */}
								<Button
									asChild
									variant="ghost"
									size="icon"
									className="relative flex size-10 cursor-pointer items-center justify-center"
								>
									<div className="relative flex h-10 w-10 items-center justify-center gap-2">
										<Bell className="h-4 w-4" />

										{/* Notification Badge */}
										{notificationCount > 0 && (
											<Badge className="absolute top-0 right-0 h-5 min-w-5 rounded-full px-1 font-mono tabular-nums">
												{notificationCount}
											</Badge>
										)}
									</div>
								</Button>

								{/* User Avatar */}
								<img
									className="hover:ring-primary relative h-10 w-10 cursor-pointer rounded-full transition-all hover:ring-2 hover:ring-offset-2"
									src={userAvatar}
									alt="User avatar"
								/>
							</div>
						</div>
					)}
				</div>
				<div className="flex flex-1 items-center justify-between self-stretch">
					<p className="text-foreground text-2xl font-bold">
						{selectedOrganisation?.name || "Your organisation"}
					</p>
					<div className="flex items-center gap-3">
						{showHeaderDate && (
							<div className="flex items-center space-x-2 text-sm">
								<Calendar size={14} />
								<div>{dayjs().format("MMMM DD, YYYY")}</div>
							</div>
						)}
						<Button
							variant="outline"
							size="icon"
							className="cursor-pointer"
							onClick={() => window.location.reload()}
						>
							<RefreshCcw className="text-muted-foreground h-4 w-4" />
						</Button>
					</div>
				</div>
			</div>
		</header>
	);
};
