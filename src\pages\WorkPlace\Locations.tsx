import { useEffect, useState, type FC } from "react";
import {
	LocationOrganizationCard,
	LocationsList,
	OrganizationDetailsSheet,
	type Organization,
} from "@/features/locations";
import { useUIStore } from "@/stores/uiStore";

const Locations: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/workplace",
			},
			{
				label: "Locations",
				isCurrentPage: true,
			},
		]);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	const handleView = (org: Organization) => {
		console.log("View:", org.name);
		setOpenOrganizationDetailsSheet(true);
	};

	return (
		<div className="flex flex-col gap-4">
			<LocationOrganizationCard
				organization={{
					id: "org-1",
					name: "HealthCare Plus Medical Center",
					address:
						"123 Main Street, Downtown Medical District, NY 10001",
					rating: 4,
					locationsCount: 4,
					providersCount: 20,
					averageWaitTime: "2 hr 30 mins",
				}}
				onView={handleView}
				onEdit={(org) => console.log("Edit:", org.name)}
				onDelete={(org) => console.log("Delete:", org.name)}
			/>
			<LocationsList />
			<OrganizationDetailsSheet
				open={openOrganizationDetailsSheet}
				onClose={() => setOpenOrganizationDetailsSheet(false)}
			/>
		</div>
	);
};

export default Locations;
