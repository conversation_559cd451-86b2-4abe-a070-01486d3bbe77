import React, { useCallback, useState } from "react";
import {
	Upload,
	FileText,
	Edit2,
	Trash2,
	Loader2,
	ImageIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import type { UploaderProps, FileItemProps, UploadedFile } from "./types";

/**
 * File item component for displaying uploaded files
 */
const FileItem = React.forwardRef<HTMLDivElement, FileItemProps>(
	({ file, onRemove, onEdit, className }, ref) => {
		const [isEditing, setIsEditing] = useState(false);
		const [editName, setEditName] = useState(file.name);

		const handleEdit = () => {
			if (isEditing) {
				onEdit?.(file.id, editName);
				setIsEditing(false);
			} else {
				setIsEditing(true);
			}
		};

		const handleKeyDown = (e: React.KeyboardEvent) => {
			if (e.key === "Enter") {
				handleEdit();
			} else if (e.key === "Escape") {
				setEditName(file.name);
				setIsEditing(false);
			}
		};

		const formatFileSize = (bytes: number) => {
			if (bytes === 0) return "0 Bytes";
			const k = 1024;
			const sizes = ["Bytes", "KB", "MB", "GB"];
			const i = Math.floor(Math.log(bytes) / Math.log(k));
			return (
				parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
			);
		};

		return (
			<div
				ref={ref}
				className={cn(
					"bg-background border-border flex items-center gap-3 rounded-lg border p-3",
					"hover:bg-accent/50 transition-colors",
					className
				)}
			>
				<div className="flex-shrink-0">
					<div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
						<FileText className="text-primary h-5 w-5" />
					</div>
				</div>

				<div className="min-w-0 flex-1">
					{isEditing ? (
						<Input
							value={editName}
							onChange={(e) => setEditName(e.target.value)}
							onBlur={handleEdit}
							onKeyDown={handleKeyDown}
							className="h-6 text-sm"
							autoFocus
						/>
					) : (
						<div>
							<p className="text-foreground truncate text-sm font-medium">
								{file.name}
							</p>
							<p className="text-muted-foreground text-xs">
								{formatFileSize(file.size)}
							</p>
						</div>
					)}
				</div>

				<div className="flex items-center gap-1">
					<Button
						variant="ghost"
						size="sm"
						onClick={handleEdit}
						className="h-8 w-8 p-0"
					>
						<Edit2 className="h-4 w-4" />
						<span className="sr-only">Edit file name</span>
					</Button>
					<Button
						variant="ghost"
						size="sm"
						onClick={() => onRemove?.(file.id)}
						className="text-destructive hover:text-destructive h-8 w-8 p-0"
					>
						<Trash2 className="h-4 w-4" />
						<span className="sr-only">Remove file</span>
					</Button>
				</div>
			</div>
		);
	}
);
FileItem.displayName = "FileItem";

/**
 * Uploader component for file uploads with drag and drop support
 *
 * @example
 * ```tsx
 * <Uploader
 *   onFilesChange={(files) => console.log('Files selected:', files)}
 *   accept=".svg,.jpg,.jpeg,.png"
 *   maxFileSize={10 * 1024 * 1024} // 10MB
 *   uploadText="Click or drag file here to upload"
 *   descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
 * />
 * ```
 */
const Uploader = React.forwardRef<HTMLDivElement, UploaderProps>(
	(
		{
			files = [],
			isLoading = false,
			multiple = false,
			accept = ".svg,.jpg,.jpeg,.png",
			maxFileSize = 10 * 1024 * 1024, // 10MB
			maxFiles = multiple ? 10 : 1,
			disabled = false,
			uploadText = "Click or drag file here to upload file",
			descriptionText = "Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
			size = "default",
			variant = "default",
			className,
			uploadIcon,
			onFilesChange,
			onFileRemove,
			onFileEdit,
			onError,
			...props
		},
		ref
	) => {
		const [isDragOver, setIsDragOver] = useState(false);
		const fileInputRef = React.useRef<HTMLInputElement>(null);

		const sizeClasses = {
			sm: "p-6 min-h-[120px]",
			md: "p-8 min-h-[160px]",
			lg: "p-10 min-h-[200px]",
			default: "p-2 min-h-[68px]",
		};

		const variantClasses = {
			default: "border-dashed border-2",
			compact: "border border-dashed",
			bordered: "border-2 border-solid",
		};

		const validateFile = (file: File): string | null => {
			if (maxFileSize && file.size > maxFileSize) {
				return `File size exceeds ${Math.round(maxFileSize / 1024 / 1024)}MB limit`;
			}

			if (accept) {
				const acceptedTypes = accept
					.split(",")
					.map((type) => type.trim());
				const fileExtension =
					"." + file.name.split(".").pop()?.toLowerCase();
				const isAccepted = acceptedTypes.some(
					(type) =>
						type === fileExtension ||
						file.type.includes(type.replace(".", ""))
				);
				if (!isAccepted) {
					return `File type not accepted. Please use: ${accept}`;
				}
			}

			return null;
		};

		const handleFiles = useCallback(
			(newFiles: FileList | File[]) => {
				const fileArray = Array.from(newFiles);
				const validFiles: File[] = [];

				for (const file of fileArray) {
					const error = validateFile(file);
					if (error) {
						onError?.(error);
						continue;
					}

					if (files.length + validFiles.length >= maxFiles) {
						onError?.(`Maximum ${maxFiles} files allowed`);
						break;
					}

					validFiles.push(file);
				}

				if (validFiles.length > 0) {
					onFilesChange?.(validFiles);
				}
			},
			[files.length, maxFiles, onError, onFilesChange, validateFile]
		);

		const handleDragOver = useCallback(
			(e: React.DragEvent) => {
				e.preventDefault();
				e.stopPropagation();
				if (!disabled && !isLoading) {
					setIsDragOver(true);
				}
			},
			[disabled, isLoading]
		);

		const handleDragLeave = useCallback((e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setIsDragOver(false);
		}, []);

		const handleDrop = useCallback(
			(e: React.DragEvent) => {
				e.preventDefault();
				e.stopPropagation();
				setIsDragOver(false);

				if (disabled || isLoading) return;

				const droppedFiles = e.dataTransfer.files;
				if (droppedFiles.length > 0) {
					handleFiles(droppedFiles);
				}
			},
			[disabled, isLoading, handleFiles]
		);

		const handleFileInput = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				const selectedFiles = e.target.files;
				if (selectedFiles && selectedFiles.length > 0) {
					handleFiles(selectedFiles);
				}
				// Reset input
				if (fileInputRef.current) {
					fileInputRef.current.value = "";
				}
			},
			[handleFiles]
		);

		const handleUploadClick = () => {
			if (!disabled && !isLoading) {
				fileInputRef.current?.click();
			}
		};

		const hasFiles = files.length > 0;

		return (
			<div ref={ref} className={cn("space-y-4", className)} {...props}>
				{/* Upload Area */}
				<div
					className={cn(
						"relative cursor-pointer transition-all duration-200",
						"border-border bg-background rounded-lg",
						"hover:bg-accent/50 focus-within:ring-ring flex items-center justify-center focus-within:ring-2 focus-within:ring-offset-2",
						sizeClasses[size],
						variantClasses[variant],
						isDragOver && "border-primary bg-primary/5",
						(disabled || isLoading) &&
							"cursor-not-allowed opacity-50",
						className
					)}
					onDragOver={handleDragOver}
					onDragLeave={handleDragLeave}
					onDrop={handleDrop}
					onClick={handleUploadClick}
				>
					<input
						ref={fileInputRef}
						type="file"
						accept={accept}
						multiple={multiple}
						onChange={handleFileInput}
						disabled={disabled || isLoading}
						className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
					/>

					<div className="flex flex-1 items-center justify-start gap-4 text-center">
						<div className="bg-muted flex h-[52px] w-[52px] items-center justify-center rounded-lg">
							{isLoading ? (
								<Loader2 className="text-muted-foreground h-6 w-6 animate-spin" />
							) : (
								uploadIcon || (
									<ImageIcon className="text-muted-foreground h-6 w-6" />
								)
							)}
						</div>

						<div className="flex flex-1 flex-col items-start justify-center space-y-1">
							<p className="text-foreground text-sm font-medium">
								{uploadText}
							</p>
							<p className="text-muted-foreground text-xs">
								{descriptionText}
							</p>
						</div>
					</div>
				</div>

				{/* Uploaded Files */}
				{hasFiles && (
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<h4 className="text-foreground text-sm font-medium">
								Uploaded Files ({files.length})
							</h4>
							{multiple && (
								<Badge variant="secondary" className="text-xs">
									{files.length}/{maxFiles}
								</Badge>
							)}
						</div>

						<div className="space-y-2">
							{files.map((file) => (
								<FileItem
									key={file.id}
									file={file}
									onRemove={onFileRemove}
									onEdit={onFileEdit}
								/>
							))}
						</div>
					</div>
				)}
			</div>
		);
	}
);
Uploader.displayName = "Uploader";

export { Uploader, FileItem };
export type { UploaderProps, FileItemProps };
