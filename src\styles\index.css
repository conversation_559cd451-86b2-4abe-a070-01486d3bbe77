@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap");

@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme {
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-primary: rgb(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));

	--color-chart-1: hsl(var(--chart-1));
	--color-chart-2: hsl(var(--chart-2));
	--color-chart-3: hsl(var(--chart-3));
	--color-chart-4: hsl(var(--chart-4));
	--color-chart-5: hsl(var(--chart-5));

	--color-sidebar: hsl(var(--sidebar-background));
	--color-sidebar-foreground: hsl(var(--sidebar-foreground));
	--color-sidebar-primary: hsl(var(--sidebar-primary));
	--color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
	--color-sidebar-accent: hsl(var(--sidebar-accent));
	--color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
	--color-sidebar-border: hsl(var(--sidebar-border));
	--color-sidebar-ring: hsl(var(--sidebar-ring));

	--color-shadow-location-card: hsl(var(--shadow-location-card));
	--color-subtle-subtle: hsl(var(--subtle));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@layer utilities {
	body {
		font-family: Arial, Helvetica, sans-serif;
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 0 0% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 0 0% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 3.9%;
		--primary: 0 89 148;
		--primary-foreground: 0 0% 98%;
		--secondary: 0 0% 96.1%;
		--secondary-foreground: 0 0% 9%;
		--muted: 0 0% 96.1%;
		--muted-foreground: 0 0% 45.1%;
		--accent: 0 0% 96.1%;
		--accent-foreground: 0 0% 9%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 89.8%;
		--input: 0 0% 89.8%;
		--ring: 0 0% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
		--shadow-location-card: 0px 4px 20px 0px hsla(204, 15%, 66%, 0.18);
		/* --color-subtle: 0 0% 98%;background: var(--Foreground-Subtle, hsla(0, 0%, 98%, 1)); */

		--subtle: 0 0% 98%;
	}
	.dark {
		--background: 0 0% 3.9%;
		--foreground: 0 0% 98%;
		--card: 0 0% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 0 0% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 0 0% 9%;
		--secondary: 0 0% 14.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 0 0% 14.9%;
		--muted-foreground: 0 0% 63.9%;
		--accent: 0 0% 14.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 14.9%;
		--input: 0 0% 14.9%;
		--ring: 0 0% 83.1%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
		--shadow-location-card: 204 15% 66%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

body {
	margin: 0;
	min-height: 100vh;
	font-family: "DM Sans", system-ui, sans-serif;
	background-color: hsl(var(--background));
	color: hsl(var(--foreground));
}

* {
	box-sizing: border-box;
}
