// 📋 SHEET USAGE GUIDE - Replacing Drawer with Sheet

import React from "react";
import { useModal, useSheet } from "@/lib/hooks/useModal";
import { Button } from "@/components/ui/button";

// 🎯 BASIC SHEET USAGE EXAMPLES

export function SheetUsageExamples() {
	const { openModal, closeModal, isOpen: isModalOpen } = useModal();
	const { openSheet, closeSheet, isOpen: isSheetOpen } = useSheet();

	// ✅ Simple Sheet Usage (replaces drawer)
	const handleSimpleSheet = () => {
		openSheet("user-profile", {
			direction: "right", // Maps to "side" prop: right, left, top, bottom
			size: "md",
			data: {
				userId: "123",
				title: "User Profile",
				description: "View and edit user information",
				mode: "view",
			},
		});
	};

	// ✅ Sheet with Custom Configuration
	const handleAdvancedSheet = () => {
		openSheet("notifications", {
			direction: "left", // Sheet will slide from left
			size: "sm",
			dismissible: true,
			data: {
				filter: "unread",
				title: "Notifications",
				description: "Your recent notifications",
				onMarkAsRead: (notificationId: string) => {
					console.log("Marked as read:", notificationId);
				},
			},
		});
	};

	// ✅ Full-height Sheet
	const handleFullSheet = () => {
		openSheet("settings", {
			direction: "right",
			size: "lg",
			data: {
				title: "Application Settings",
				description: "Configure your application preferences",
				section: "general",
			},
		});
	};

	// ✅ Top/Bottom Sheets (like mobile bottom sheets)
	const handleBottomSheet = () => {
		openSheet("customer-details", {
			direction: "bottom",
			size: "md",
			data: {
				customerId: "456",
				title: "Customer Details",
				description: "View customer information",
			},
		});
	};

	// ✅ Conditional Rendering Based on State
	const conditionalActions = () => {
		if (isModalOpen("confirmation")) {
			return <p>Confirmation modal is open</p>;
		}

		if (isSheetOpen("user-profile")) {
			return <p>User profile sheet is open</p>;
		}

		return <p>No modals or sheets open</p>;
	};

	return (
		<div className="space-y-4 p-6">
			<h2 className="text-2xl font-bold">
				Sheet Examples (Replacing Drawers)
			</h2>

			{/* Sheet Examples */}
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">Sheets</h3>
				<div className="grid grid-cols-2 gap-2">
					<Button onClick={handleSimpleSheet}>
						Open Right Sheet
					</Button>
					<Button onClick={handleAdvancedSheet}>
						Open Left Sheet
					</Button>
					<Button onClick={handleFullSheet}>Open Large Sheet</Button>
					<Button onClick={handleBottomSheet}>
						Open Bottom Sheet
					</Button>
				</div>
			</div>

			{/* Status */}
			<div className="mt-4">{conditionalActions()}</div>
		</div>
	);
}

// 🛠️ CUSTOM SHEET COMPONENT EXAMPLE
interface CustomSheetProps {
	data: {
		title: string;
		content: React.ReactNode;
		onSave: (formData: any) => void;
	};
	onClose: () => void;
}

export function CustomSheet({ data, onClose }: CustomSheetProps) {
	const { title, content, onSave } = data;

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		const formData = new FormData(e.target as HTMLFormElement);
		onSave(Object.fromEntries(formData));
		onClose();
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-4 p-4">
			<h2 className="text-xl font-bold">{title}</h2>
			{content}
			<div className="flex justify-end space-x-2">
				<Button type="button" variant="outline" onClick={onClose}>
					Cancel
				</Button>
				<Button type="submit">Save</Button>
			</div>
		</form>
	);
}

// 🎯 MIGRATION GUIDE: DRAWER TO SHEET

/*
📝 MIGRATION STEPS:

1. Replace imports:
   - OLD: import { DrawerManager } from "@/components/ui-components/DrawerManager";
   - NEW: import { SheetManager } from "@/components/ui-components/SheetManager";

2. Replace hooks:
   - OLD: import { useDrawer } from "@/lib/hooks/useModal";
   - NEW: import { useSheet } from "@/lib/hooks/useModal";

3. Update function calls:
   - OLD: const { openDrawer, closeDrawer, isOpen } = useDrawer();
   - NEW: const { openSheet, closeSheet, isOpen } = useSheet();

4. Update API calls:
   - OLD: openDrawer("id", { direction: "right", size: "md", data: {} })
   - NEW: openSheet("id", { direction: "right", size: "md", data: {} })

5. Replace component in App:
   - OLD: <DrawerManager />
   - NEW: <SheetManager />

✅ BENEFITS OF USING SHEET:
- Better Radix UI integration
- More consistent with design systems
- Better accessibility out of the box
- Smoother animations
- Better mobile support
*/

// 🚀 ADVANCED SHEET PATTERNS

export function useSheetWithConfirmation() {
	const { openSheet, closeSheet } = useSheet();
	const { openModal } = useModal();

	const openSheetWithConfirmClose = (
		sheetId: string,
		options: any,
		confirmMessage = "Are you sure you want to close? Unsaved changes will be lost."
	) => {
		openSheet(sheetId, {
			...options,
			dismissible: false, // Prevent auto-close
			data: {
				...options.data,
				onAttemptClose: () => {
					openModal("confirmation", {
						data: {
							title: "Confirm Close",
							message: confirmMessage,
							onConfirm: () => closeSheet(),
						},
					});
				},
			},
		});
	};

	return { openSheetWithConfirmClose };
}

// 📱 RESPONSIVE SHEET USAGE

export function useResponsiveSheet() {
	const { openSheet } = useSheet();

	const openResponsiveSheet = (sheetId: string, options: any) => {
		const isMobile = window.innerWidth < 768;

		openSheet(sheetId, {
			...options,
			direction: isMobile ? "bottom" : "right",
			size: isMobile ? "full" : options.size || "md",
		});
	};

	return { openResponsiveSheet };
}
